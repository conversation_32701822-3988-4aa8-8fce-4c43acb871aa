import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/services/api';

export interface CategorySkeleton {
  id: number;
  parentId: number | null;
  name: string;
  path: string;
  level: number;
  datasetCnt?: number;
}

export function useTreeSkeleton(withCounts: boolean = true) {
  return useQuery({
    queryKey: ['tree-skeleton', withCounts],
    queryFn: () => api.getTreeSkeleton(withCounts),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useVarNodes(key: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['var-nodes', key],
    queryFn: () => api.getVarNodes(key),
    enabled: enabled && !!key && key.trim().length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useJobEffectiveVars(templateId: number, datasetIds: number[], enabled: boolean = true) {
  return useQuery({
    queryKey: ['job-effective-vars', templateId, datasetIds],
    queryFn: () => api.getJobEffectiveVars(templateId, datasetIds),
    enabled: enabled && templateId > 0 && datasetIds.length > 0,
    staleTime: 1 * 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
}
